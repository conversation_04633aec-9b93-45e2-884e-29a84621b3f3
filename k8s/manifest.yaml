apiVersion: apps/v1
kind: Deployment
metadata:
  name: oa-{{app_name}}-deployment-{{env_name}}
  namespace: oa-{{env_name}}
  labels:
    k8s-app: oa-{{app_name}}-{{env_name}}
spec:
  replicas: 2
  revisionHistoryLimit: 1
  progressDeadlineSeconds: 420
  selector:
    matchLabels:
      k8s-app: oa-{{app_name}}-{{env_name}}
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 0
      maxSurge: 1
  template:
    metadata:
      labels:
        k8s-app: oa-{{app_name}}-{{env_name}}
    spec:
      containers:
      - name: oa-{{app_name}}-{{env_name}}
        image: {{gcr_uri}}/oa-{{app_name}}-{{env_name}}:{{image_id}}
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 3000
          name: http
        readinessProbe:
          tcpSocket:
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        livenessProbe:
          tcpSocket:
            port: 3000
          initialDelaySeconds: 15
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        resources:
          limits:
            cpu: "0.2"
            memory: "512Mi"
          requests:
            cpu: "0.05"
            memory: "128Mi"
---
apiVersion: v1
kind: Service
metadata:
  name: oa-{{app_name}}-service-{{env_name}}
  namespace: oa-{{env_name}}
spec:
  ports:
  - port: 3000
    targetPort: http
    protocol: TCP
    name: http
  selector:
    k8s-app: oa-{{app_name}}-{{env_name}}
  type: ClusterIP
---
apiVersion: gateway.networking.k8s.io/v1
kind: HTTPRoute
metadata:
  name: oa-{{app_name}}-httproute-{{env_name}}
  namespace: oa-{{env_name}}
spec:
  parentRefs:
  - kind: Gateway
    name: gke-gateway-pvt-{{overarching_env}}
    namespace: entrypoint-pvt
  hostnames:
  - "{{pvt_url}}"
  rules:
  - matches:
    - path:
        type: PathPrefix
        value: /
    backendRefs:
    - name: oa-{{app_name}}-service-{{env_name}}
      port: 3000
---
apiVersion: networking.gke.io/v1
kind: HealthCheckPolicy
metadata:
  name: oa-{{app_name}}-healthcheck-{{env_name}}
  namespace: oa-{{env_name}}
spec:
  default:
    checkIntervalSec: 15
    timeoutSec: 10
    healthyThreshold: 2
    unhealthyThreshold: 3
    logConfig:
      enabled: true
    config:
      type: HTTP
      httpHealthCheck:
        portSpecification: USE_FIXED_PORT
        port: 3000
        requestPath: /api/healthz
  targetRef:
    group: ""
    kind: Service
    name: oa-{{app_name}}-service-{{env_name}}
---
apiVersion: networking.gke.io/v1
kind: GCPBackendPolicy
metadata:
  name: oa-{{app_name}}-backend-policy-{{env_name}}
  namespace: oa-{{env_name}}
spec:
  default:
    timeoutSec: 180
    logging:
      enabled: True
      sampleRate: 1000000
  targetRef:
    group: ""
    kind: Service
    name: oa-{{app_name}}-service-{{env_name}}