"use client";

import React from "react";
import { useFormContext } from "react-hook-form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { RelationshipConfig } from "@/lib/schema/FormGenerator";
import { RelationshipSelect } from "@/components/forms/widgets/RelationshipSelect";
import { OneToManyManager } from "@/components/forms/widgets/OneToManyManager";
import { Field, Schema } from "@/lib/schema/types";
import { OneToOneManager } from "./widgets/OneToOneManager";

interface RelationshipRendererProps {
  relationship: RelationshipConfig;
  relatedSchema: Schema;
  parentId?: string;
  mode?: "create" | "edit";
  relatedSchemas?: Record<string, Schema>;
}

export const RelationshipRenderer: React.FC<RelationshipRendererProps> = ({
  relationship,
  relatedSchema,
  parentId,
  mode,
  relatedSchemas = {},
}) => {
  const { control } = useFormContext();

  // Use OneToManyManager for one-to-many relationships
  if (relationship.type === "one-to-many") {
    return (
      <OneToManyManager
        relationship={relationship}
        fieldName={relationship.name}
        relatedSchema={relatedSchema}
        className="mb-6"
        parentId={parentId}
        relatedSchemas={relatedSchemas}
      />
    );
  }
  if (relationship.type === "one-to-one") {
    return (
      <OneToOneManager
        relationship={relationship}
        fieldName={relationship.name}
        relatedSchema={relatedSchema}
        className="mb-6"
        parentId={parentId}
        relatedSchemas={relatedSchemas}
      />
    );
  }


  // Determine if this relationship should be disabled in edit mode
  // For many-to-one and one-to-one relationships, they represent foreign keys that shouldn't be changed
  const shouldDisableInEdit =
    mode === "edit" &&
    (relationship.type === "many-to-one");

  // Use RelationshipSelect for other relationship types
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-medium">
          {relationship.title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <RelationshipSelect
          name={relationship.name}
          control={control}
          relationshipType={relationship.type}
          targetComponent={relationship.targetComponent || ""}
          displayField={
            relatedSchema?.schema_definition?.fields?.find(
              (f: Field) => f.name === relationship.sourceField
            )?.ui_config?.display_field
          }
          placeholder={`Select ${relationship.title}`}
          disabled={shouldDisableInEdit}
        />
      </CardContent>
    </Card>
  );
};
