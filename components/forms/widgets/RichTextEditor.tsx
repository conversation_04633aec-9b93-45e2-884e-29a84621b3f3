"use client";

import React from "react";
import { Control, Controller } from "react-hook-form";
// import { CKEditor } from "@ckeditor/ckeditor5-react";
// import { ClassicEditor } from "ckeditor5";

import dynamic from "next/dynamic";
import "react-quill-new/dist/quill.snow.css";
const ReactQuill = dynamic(() => import("react-quill-new"), { ssr: false });

export const d = "force-dynamic";

// Quill needs to be dynamically imported in Next.js to avoid SSR issues

type RichTextEditorProps = {
  name: string;
  control: Control<any>;
  placeholder?: string;
  disabled?: boolean;
  error?: string;
  validation?: any;
  value?: string;
  onChange?: (content: string) => void;
};

export const RichTextEditor: React.FC<RichTextEditorProps> = ({
  name,
  control,
  placeholder,
  disabled,
  error,
  validation,
  value,
  onChange,
}) => {
  // If value and onChange are provided, use controlled mode
  if (value !== undefined && onChange) {
    return (
      <div className="space-y-2">
        <div
          className={`border rounded-md ${
            error ? "border-destructive" : "border-gray-200"
          } ${disabled ? "opacity-50" : ""}`}
        >
          <ReactQuill
            theme="snow"
            value={value}
            onChange={(content, delta, source, editor) => {
              onChange(content);
            }}
            placeholder={placeholder || "Enter your content..."}
            modules={{
              toolbar: [
                [{ header: [1, 2, 3, 4, 5, 6, false] }],
                ["bold", "italic", "underline"],
                [{ list: "ordered" }, { list: "bullet" }],
                [{ indent: "-1" }, { indent: "+1" }],
                ["link", "blockquote"],
                ["clean"],
              ],
            }}
            readOnly={disabled}
          />
        </div>
      </div>
    );
  }

  return (
    <Controller
      name={name}
      control={control}
      rules={validation}
      render={({ field }) => (
        <div className="space-y-2">
          <div
            className={`border rounded-md ${
              error ? "border-destructive" : "border-gray-200"
            } ${disabled ? "opacity-50" : ""}`}
          >
            <ReactQuill
              theme="snow"
              value={field.value || ""}
              onChange={(content, delta, source, editor) => {
                field.onChange(content); // content is the HTML string
              }}
              onBlur={() => {
                field.onBlur();
              }}
              placeholder={placeholder || "Enter your content..."}
              modules={{
                toolbar: [
                  [{ header: [1, 2, 3, 4, 5, 6, false] }],
                  ["bold", "italic", "underline"],
                  [{ list: "ordered" }, { list: "bullet" }],
                  [{ indent: "-1" }, { indent: "+1" }],
                  ["link", "blockquote"],
                  ["clean"],
                ],
              }}
              readOnly={disabled}
            />

            {/* <CKEditor
              
              editor={ClassicEditor}
              data={field.value || ""}
              onChange={(event, editor) => {
                const data = editor.getData();
                field.onChange(data);
              }}
              onBlur={(event, editor) => {
                field.onBlur();
              }}
              disabled={disabled}
              config={{
                placeholder: placeholder || "Enter your content...",
                toolbar: [
                  "heading",
                  "|",
                  "bold",
                  "italic",
                  "underline",
                  "|",
                  "bulletedList",
                  "numberedList",
                  "|",
                  "outdent",
                  "indent",
                  "|",
                  "link",
                  "blockQuote",
                  "|",
                  "insertTable",
                  "|",
                  "undo",
                  "redo",
                ],
                heading: {
                  options: [
                    { model: "paragraph", title: "Paragraph", class: "ck-heading_paragraph" },
                    { model: "heading1", view: "h1", title: "Heading 1", class: "ck-heading_heading1" },
                    { model: "heading2", view: "h2", title: "Heading 2", class: "ck-heading_heading2" },
                    { model: "heading3", view: "h3", title: "Heading 3", class: "ck-heading_heading3" },
                  ],
                },
                table: {
                  contentToolbar: ["tableColumn", "tableRow", "mergeTableCells"],
                },
              }}
            /> */}
          </div>
        </div>
      )}
    />
  );
};
