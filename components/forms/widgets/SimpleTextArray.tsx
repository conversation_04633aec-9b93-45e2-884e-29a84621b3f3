"use client";

import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { X, Plus, Edit2, Check, XCircle } from "lucide-react";

// Quill needs to be dynamically imported in Next.js to avoid SSR issues
import dynamic from "next/dynamic";
import "react-quill-new/dist/quill.snow.css";
const ReactQuill = dynamic(() => import("react-quill-new"), { ssr: false });
import { 
  useTextArrayState, 
  createTextArrayActions, 
  getItemCountText, 
  getItemCountDisplay 
} from "@/lib/utils/textArrayUtils";

interface SimpleTextArrayProps {
  value: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  disabled?: boolean;
  maxItems?: number;
  helpText?: string;
  widget?: string;
}

export const SimpleTextArray: React.FC<SimpleTextArrayProps> = ({
  value = [],
  onChange,
  placeholder = "Enter text",
  disabled = false,
  maxItems = 50,
  helpText,
  widget,
}) => {
  const [state, setState] = useState(useTextArrayState());
  const items: string[] = Array.isArray(value) ? value : [];
  
  const actions = createTextArrayActions(
    state, 
    setState, 
    items, 
    onChange, 
    maxItems, 
    widget || ""
  );

  if (widget === "rich_text") {
    return (
      <div className="border-2 border-dashed border-border rounded-lg p-4 bg-background/50">
        
        {/* Input row */}
        <div className="flex gap-2 mb-3">
          <div className="flex-1 border rounded-md">
            <ReactQuill
              theme="snow"
              value={state.inputValue}
              onChange={(content) => actions.setInputValue(content)}
              placeholder={placeholder}
              modules={{
                toolbar: [
                  [{ header: [1, 2, 3, 4, 5, 6, false] }],
                  ["bold", "italic", "underline"],
                  [{ list: "ordered" }, { list: "bullet" }],
                  [{ indent: "-1" }, { indent: "+1" }],
                  ["link", "blockquote"],
                  ["clean"],
                ],
              }}
              readOnly={disabled}
            />
          </div>
          <Button
            type="button"
            onClick={actions.addItem}
            disabled={
              disabled || !state.inputValue.trim() || items.length >= maxItems
            }
            size="sm"
            variant="outline"
            className="flex-shrink-0"
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>

        {/* Items display */}
        {items.length > 0 && (
          <div className="space-y-4">
            {items.map((item, index) => (
              <div
                key={index}
                className="border border-border rounded-md p-4 bg-card shadow-sm"
              >
                {state.editingIndex === index ? (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-muted-foreground">
                        Editing Item #{index + 1}
                      </span>
                      <div className="flex gap-2">
                        <Button
                          type="button"
                          onClick={actions.saveEdit}
                          size="sm"
                          variant="default"
                          className="h-8"
                        >
                          <Check className="h-3 w-3 mr-1" />
                          Save
                        </Button>
                        <Button
                          type="button"
                          onClick={actions.cancelEdit}
                          size="sm"
                          variant="outline"
                          className="h-8"
                        >
                          <XCircle className="h-3 w-3 mr-1" />
                          Cancel
                        </Button>
                      </div>
                    </div>
                    <div className="border rounded-md">
                      <div className="space-y-2">
                        <div
                          className={`border rounded-md border-gray-200
                          ${disabled ? "opacity-50" : ""}`}
                        >
                          <ReactQuill
                            theme="snow"
                            value={state.editValue}
                            onChange={(content) =>
                              actions.setEditValue(content)
                            }
                            placeholder="Edit rich text content..."
                            modules={{
                              toolbar: [
                                [{ header: [1, 2, 3, 4, 5, 6, false] }],
                                ["bold", "italic", "underline"],
                                [{ list: "ordered" }, { list: "bullet" }],
                                [{ indent: "-1" }, { indent: "+1" }],
                                ["link", "blockquote"],
                                ["clean"],
                              ],
                            }}
                            readOnly={disabled}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-xs font-medium text-muted-foreground">
                        Item #{index + 1}
                      </span>
                      {!disabled && (
                        <div className="flex gap-1">
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-7 w-7 p-0"
                            onClick={() => actions.startEdit(index)}
                            title="Edit item"
                          >
                            <Edit2 className="h-3 w-3" />
                          </Button>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="h-7 w-7 p-0 hover:bg-destructive hover:text-destructive-foreground"
                            onClick={() => actions.removeItem(index)}
                            title="Remove item"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      )}
                    </div>
                    <div className="prose prose-sm max-w-none">
                      <div
                        dangerouslySetInnerHTML={{ __html: item }}
                        className="min-h-[60px] p-3 bg-muted/30 rounded border"
                      />
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Item count indicator */}
        <div className="mt-3 pt-2 border-t border-border/50">
          <p className="text-xs text-muted-foreground">
            {getItemCountText(items, maxItems)}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="border-2 border-dashed border-border rounded-lg p-4 bg-background/50">
      {/* Input row */}
      <div className="flex gap-2 mb-3">
        <Input
          value={state.inputValue}
          onChange={(e) => actions.setInputValue(e.target.value)}
          onKeyDown={actions.handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
        />
        <Button
          type="button"
          onClick={actions.addItem}
          disabled={
            disabled || !state.inputValue.trim() || items.length >= maxItems
          }
          size="sm"
          variant="outline"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      {/* Items display */}
      {items.length > 0 && (
        <div className="space-y-2">
          {items.map((item, index) => (
            <div
              key={index}
              className="border border-border rounded-md p-3 bg-card shadow-sm"
            >
              {state.editingIndex === index ? (
                <div className="space-y-2">
                  <Input
                    value={state.editValue}
                    onChange={(e) => actions.setEditValue(e.target.value)}
                    onKeyDown={actions.handleEditKeyPress}
                    className="min-h-[60px] resize-none"
                    placeholder="Edit text..."
                    autoFocus
                  />
                  <div className="flex gap-2">
                    <Button
                      type="button"
                      onClick={actions.saveEdit}
                      size="sm"
                      variant="default"
                      className="h-7"
                    >
                      <Check className="h-3 w-3 mr-1" />
                      Save
                    </Button>
                    <Button
                      type="button"
                      onClick={actions.cancelEdit}
                      size="sm"
                      variant="outline"
                      className="h-7"
                    >
                      <XCircle className="h-3 w-3 mr-1" />
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="flex items-start justify-between gap-3">
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-foreground break-words whitespace-pre-wrap">
                      {item}
                    </p>
                  </div>
                  <div className="flex gap-1 flex-shrink-0">
                    <Button
                      type="button"
                      onClick={() => actions.startEdit(index)}
                      size="sm"
                      variant="ghost"
                      className="h-7 w-7 p-0"
                      disabled={disabled}
                    >
                      <Edit2 className="h-3 w-3" />
                    </Button>
                    <Button
                      type="button"
                      onClick={() => actions.removeItem(index)}
                      size="sm"
                      variant="ghost"
                      className="h-7 w-7 p-0 text-destructive hover:text-destructive"
                      disabled={disabled}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Help text and item count */}
      <div className="mt-3 flex justify-between items-center text-xs text-muted-foreground">
        <span>
          {getItemCountDisplay(items, maxItems)}
        </span>
        {helpText && <span>{helpText}</span>}
      </div>

      {/* Empty state */}
      {items.length === 0 && (
        <div className="text-center py-4 text-sm text-muted-foreground">
          No items added yet. Type above and press Enter or click + to add.
        </div>
      )}
    </div>
  );
};
