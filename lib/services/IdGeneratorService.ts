import { KsuidGenerator } from "@oneassure-tech/oa-utilities";

export class IdGeneratorService {
  private static generator = new KsuidGenerator();

  static async generateId(): Promise<string> {
    return this.generator.nextID().toString();
  }

  static async generateMultipleIds(count: number): Promise<string[]> {
    const promises = Array(count)
      .fill(null)
      .map(() => this.generateId());
    return Promise.all(promises);
  }
}
