export interface ChangeDetection {
  hasMainEntityChanges: boolean;
  hasRelationshipChanges: boolean;
  changedRelationships: {
    [relationshipName: string]: {
      added: any[];
      updated: any[];
      deleted: any[];
    };
  };
  mainEntityData: any;
}

export class ChangeDetector {
  static detectChanges(
    originalData: any,
    currentData: any,
    relationships: any[]
  ): ChangeDetection {
    const result: ChangeDetection = {
      hasMainEntityChanges: false,
      hasRelationshipChanges: false,
      changedRelationships: {},
      mainEntityData: {},
    };

    // Extract main entity fields (non-relationship fields)
    const mainEntityFields = { ...currentData };
    const originalMainFields = { ...originalData };

    // Remove relationship fields from main entity comparison
    relationships.forEach((rel) => {
      delete mainEntityFields[rel.name];
      delete originalMainFields[rel.name];
    });

    // Check for main entity changes
    result.hasMainEntityChanges = this.hasObjectChanged(
      originalMainFields,
      mainEntityFields
    );
    result.mainEntityData = mainEntityFields;

    // Check for relationship changes
    relationships.forEach((relationship) => {
      const originalRelData = originalData[relationship.name];
      const currentRelData = currentData[relationship.name];

      // Skip if both are null/undefined (no changes)
      if (originalRelData == null && currentRelData == null) {
        return;
      }

      const relationshipChanges = this.detectRelationshipChanges(
        originalRelData,
        currentRelData
      );

      if (
        relationshipChanges.added.length > 0 ||
        relationshipChanges.updated.length > 0 ||
        relationshipChanges.deleted.length > 0
      ) {
        result.hasRelationshipChanges = true;
        result.changedRelationships[relationship.name] = relationshipChanges;
      }
    });

    return result;
  }

  private static hasObjectChanged(original: any, current: any): boolean {
    // Handle null/undefined cases
    if (original === null && current === null) return false;
    if (original === undefined && current === undefined) return false;
    if (original === null || current === null) return true;
    if (original === undefined || current === undefined) return true;

    const originalKeys = Object.keys(original).filter(
      (key) => !key.startsWith("_")
    );
    const currentKeys = Object.keys(current).filter(
      (key) => !key.startsWith("_")
    );

    // Get all unique keys from both objects
    const allKeys = [...new Set([...originalKeys, ...currentKeys])];

    for (const key of allKeys) {
      const originalValue = original[key];
      const currentValue = current[key];

      // Check if key exists in both objects
      const originalHasKey = originalKeys.includes(key);
      const currentHasKey = currentKeys.includes(key);

      if (originalHasKey !== currentHasKey) {
        return true;
      }

      // Use deep comparison for values
      if (!this.isEqual(originalValue, currentValue)) {
        console.log("Object changed: different value for key", {
          key,
          originalValue,
          currentValue,
          originalType: typeof originalValue,
          currentType: typeof currentValue,
        });
        return true;
      }
    }

    return false;
  }

  /**
   * Deep equality comparison that handles common data types
   */
  private static isEqual(a: any, b: any): boolean {
    // Strict equality check
    if (a === b) return true;

    // Handle null/undefined
    if (a == null || b == null) return a === b;

    // Handle different types
    if (typeof a !== typeof b) {
      // Special case: string/number comparison (form inputs often convert)
      if (
        (typeof a === "string" && typeof b === "number") ||
        (typeof a === "number" && typeof b === "string")
      ) {
        return String(a) === String(b);
      }
      return false;
    }

    // Handle arrays
    if (Array.isArray(a) && Array.isArray(b)) {
      if (a.length !== b.length) return false;
      for (let i = 0; i < a.length; i++) {
        if (!this.isEqual(a[i], b[i])) return false;
      }
      return true;
    }

    // Handle objects
    if (typeof a === "object" && typeof b === "object") {
      const keysA = Object.keys(a);
      const keysB = Object.keys(b);

      if (keysA.length !== keysB.length) return false;

      for (const key of keysA) {
        if (!keysB.includes(key)) return false;
        if (!this.isEqual(a[key], b[key])) return false;
      }
      return true;
    }

    // For primitive types that passed strict equality, they're different
    return false;
  }

  private static detectRelationshipChanges(original: any, current: any) {
    const added: any[] = [];
    const updated: any[] = [];
    const deleted: any[] = [];

    // Handle different relationship types and data structures
    const originalArray = this.normalizeRelationshipData(original);
    const currentArray = this.normalizeRelationshipData(current);

    // Create maps for easier lookup
    const originalMap = new Map();
    const currentMap = new Map();

    console.log("Original: ", original);
    console.log("Normalized original array: ", originalArray);
    console.log("Current: ", current);
    console.log("Normalized current array: ", currentArray);
    
    // Safely process original array
    if (Array.isArray(originalArray)) {
      originalArray.forEach((item) => {
        if (item && item.id) {
          originalMap.set(item.id, item);
        }
      });
    }

    // Safely process current array
    if (Array.isArray(currentArray)) {
      currentArray.forEach((item) => {
        if (item && item.id) {
          currentMap.set(item.id, item);
        }
      });
    }

    // Find added items (exist in current but not in original)
    if (Array.isArray(currentArray)) {
      currentArray.forEach((item) => {
        if (!item || !item.id || !originalMap.has(item.id)) {
          // New item (no ID or ID not in original)
          added.push(item);
        } else {
          // Existing item - check if updated
          const originalItem = originalMap.get(item.id);
          if (this.hasObjectChanged(originalItem, item)) {
            console.log(`Item ${item.id} has changes:`, {
              original: originalItem,
              current: item,
            });
            updated.push(item);
          }
        }
      });
    }

    // Find deleted items (exist in original but not in current)
    if (Array.isArray(originalArray)) {
      originalArray.forEach((item) => {
        if (item && item.id && !currentMap.has(item.id)) {
          deleted.push(item);
        }
      });
    }

    // Only return changes if there are actual changes
    if (added.length === 0 && updated.length === 0 && deleted.length === 0) {
      return { added: [], updated: [], deleted: [] };
    }

    console.log(`Relationship changes detected:`, {
      added: added.length,
      updated: updated.length,
      deleted: deleted.length,
    });

    return { added, updated, deleted };
  }

  /**
   * Normalize relationship data to always return an array
   * Handles many-to-one (objects), one-to-many (arrays), and null/undefined cases
   */
  private static normalizeRelationshipData(data: any): any[] {
    if (data === null || data === undefined) {
      return [];
    }
    
    if (Array.isArray(data)) {
      // Filter out null/undefined items from arrays
      return data.filter(item => item != null);
    }
    
    if (typeof data === 'object') {
      // Handle many-to-one relationships that return single objects
      // Check if it has an id field (likely a relationship object)
      if (data.id !== undefined) {
        return [data];
      }
      
      // If it's an object without id, it might be empty or malformed
      // Check if it has any properties that suggest it's a relationship object
      const keys = Object.keys(data);
      if (keys.length > 0) {
        // It's an object with properties but no id - might be a malformed relationship
        console.warn('Relationship object without id field:', data);
        return [];
      }
      
      // Empty object
      return [];
    }
    
    // For primitive types, return empty array
    console.warn('Unexpected relationship data type:', typeof data, data);
    return [];
  }
}
