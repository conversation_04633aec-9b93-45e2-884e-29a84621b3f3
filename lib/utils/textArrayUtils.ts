import { useState } from "react";

export interface TextArrayState {
  inputValue: string;
  editingIndex: number | null;
  editValue: string;
}

export interface TextArrayActions {
  addItem: () => void;
  removeItem: (index: number) => void;
  startEdit: (index: number) => void;
  saveEdit: () => void;
  cancelEdit: () => void;
  handleKeyDown: (e: React.KeyboardEvent) => void;
  handleEditKeyPress: (e: React.KeyboardEvent) => void;
  setInputValue: (value: string) => void;
  setEditValue: (value: string) => void;
}

export const useTextArrayState = (): TextArrayState => {
  const [inputValue, setInputValue] = useState("");
  const [editingIndex, setEditingIndex] = useState<number | null>(null);
  const [editValue, setEditValue] = useState("");

  return {
    inputValue,
    editingIndex,
    editValue,
  };
};

export const createTextArrayActions = (
  state: TextArrayState,
  setState: React.Dispatch<React.SetStateAction<TextArrayState>>,
  items: string[],
  onChange: (value: string[]) => void,
  maxItems: number,
  widget: string
): TextArrayActions => {
  const setInputValue = (value: string) => {
    setState(prev => ({ ...prev, inputValue: value }));
  };

  const setEditValue = (value: string) => {
    setState(prev => ({ ...prev, editValue: value }));
  };

  const addItem = () => {
    if (state.inputValue.trim() && !items.includes(state.inputValue.trim())) {
      if (items.length < maxItems) {
        onChange([state.inputValue.trim(), ...items]);
        setInputValue("");
      }
    }
  };

  const removeItem = (index: number) => {
    const newItems = items.filter((_, i) => i !== index);
    onChange(newItems);
  };

  const startEdit = (index: number) => {
    setState(prev => ({
      ...prev,
      editingIndex: index,
      editValue: items[index]
    }));
  };

  const saveEdit = () => {
    if (state.editValue.trim() && state.editingIndex !== null) {
      const newItems = [...items];
      newItems[state.editingIndex] = state.editValue.trim();
      onChange(newItems);
      setState(prev => ({
        ...prev,
        editingIndex: null,
        editValue: ""
      }));
    }
  };

  const cancelEdit = () => {
    setState(prev => ({
      ...prev,
      editingIndex: null,
      editValue: ""
    }));
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addItem();
    }
  };

  const handleEditKeyPress = (e: React.KeyboardEvent) => {
    if (widget === "rich_text") {
      // For rich text, use Ctrl+Enter to save
      if (e.key === "Enter" && e.ctrlKey) {
        e.preventDefault();
        saveEdit();
      } else if (e.key === "Escape") {
        e.preventDefault();
        cancelEdit();
      }
    } else {
      // For regular text, use Enter (without shift) to save
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        saveEdit();
      } else if (e.key === "Escape") {
        e.preventDefault();
        cancelEdit();
      }
    }
  };

  return {
    addItem,
    removeItem,
    startEdit,
    saveEdit,
    cancelEdit,
    handleKeyDown,
    handleEditKeyPress,
    setInputValue,
    setEditValue,
  };
};

export const getItemCountText = (items: string[], maxItems: number): string => {
  return `${items.length} item${items.length !== 1 ? "s" : ""}${maxItems ? ` (max ${maxItems})` : ""}`;
};

export const getItemCountDisplay = (items: string[], maxItems: number): string => {
  return `${items.length} / ${maxItems} items`;
};
